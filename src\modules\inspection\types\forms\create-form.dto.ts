export interface ICreateFormDTO {
	title: string;
	text?: string;
	nomenclature: string;
	developerId: number;
	approverId: number;
	fields: Field[];
}

export interface Field {
	fieldId: number;
	nickname: string;
	required: boolean;
	group: number;
	sequence: number;
	typeId: number;
	measureId: number;
	groupTitle: string;
	biFilter: boolean;
	options: Option[];
}

export interface Option {
	sequence: number;
	option: string;
}
