import { useCreateFormMutation } from "@/modules/inspection/hooks/form/create/create-form-mutation.hook";
import { useCreateForm } from "@/modules/inspection/hooks/form/create/create-form.hook";
import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { Modal } from "@/shared/components/custom/modal";
import { FormCreateForm } from "./form";

interface IModalCreateFormProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateForm: React.FC<IModalCreateFormProps> = ({ isOpen, onClose }) => {
	const { methods, addField, removeField, updateField, reorderFields, isFieldTypeOptions } = useCreateForm();
	const { createForm } = useCreateFormMutation();

	const handleSubmit = (data: ICreateForm) => {
		const payload = {
			...data,
			developerId: Number(data.developerId),
			approverId: Number(data.approverId),
			fields: data.fields.map(field => ({
				fieldId: field.fieldId ?? 0,
				nickname: field.nickname ?? "",
				required: field.required ?? false,
				group: field.group ?? 1,
				sequence: field.sequence ?? 1,
				typeId: Number(field.typeId),
				measureId: Number(field.measureId),
				groupTitle: field.groupTitle ?? "",
				biFilter: field.biFilter ?? false,
				options: (field.options ?? []).map((opt, idx) => ({
					sequence: opt.sequence ?? idx + 1,
					option: opt.option ?? "",
				})),
			})),
		};
		createForm(payload);
		onClose();
	};

	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[1400px] !max-w-none" title="Cadastro de Formulário">
			<FormCreateForm
				mode="create"
				onClose={onClose}
				methods={methods}
				onSubmit={handleSubmit}
				addField={addField}
				removeField={removeField}
				updateField={updateField}
				reorderFields={reorderFields}
				isFieldTypeOptions={isFieldTypeOptions}
			/>
		</Modal>
	);
};
