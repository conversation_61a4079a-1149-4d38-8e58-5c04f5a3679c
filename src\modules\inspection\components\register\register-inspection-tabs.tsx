"use client";
import { Tabs, TabsContent } from "@/shared/components/shadcn/tabs";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Circle, Component, FileText, Grid, Link, Ruler, Users } from "lucide-react";
import { useState } from "react";
import { TInspectionTabValue, useInspectionTabs } from "../../hooks/tabs/inspection-tabs.hook";

import { ITabItemRegisterInspectionTabs, RegisterInspectionHeader } from "./header/register-inspection-header";
import { CamposTab } from "./tabs/campos-tab";
import { ColaboradoresTab } from "./tabs/colaboradores-tab";
import { ModalCreateForm } from "./tabs/forms/create/modal";
import { FormulariosTab } from "./tabs/forms/list/table";
import ModalCreateMeasures from "./tabs/measures/create/modal";
import { MedidasTab } from "./tabs/measures/list/table";

export const RegisterInspectionTabs = () => {
	const { activeTab, setActiveTab, availableTabs } = useInspectionTabs();
	const [searchTerm, setSearchTerm] = useState("");
	const formModal = useModal();
	const measuresModal = useModal();

	const handleTabChange = (value: string) => {
		if (availableTabs.includes(value as TInspectionTabValue)) setActiveTab(value as TInspectionTabValue);
	};

	const tabItems: ITabItemRegisterInspectionTabs[] = [
		{
			value: "medidas",
			label: "Medidas",
			icon: Ruler,
			renderContent: searchTerm => <MedidasTab searchTerm={searchTerm} />,
			onNew: () => measuresModal.openModal(),
		},
		{
			value: "campos",
			label: "Campos",
			icon: Grid,
			renderContent: searchTerm => <CamposTab searchTerm={searchTerm} />,
			onNew: searchTerm => {
				console.log("Novo em Campos", searchTerm);
			},
		},
		{
			value: "vinculo-colaboradores",
			label: "Vínculo de colaboradores",
			icon: Users,
			renderContent: searchTerm => <ColaboradoresTab searchTerm={searchTerm} />,
			onNew: searchTerm => {
				console.log("Novo em Vínculo de colaboradores", searchTerm);
			},
		},
		{
			value: "formularios",
			label: "Formulários",
			icon: FileText,
			renderContent: searchTerm => <FormulariosTab searchTerm={searchTerm} />,
			onNew: () => formModal.openModal(),
		},
		{
			value: "celulas",
			label: "Células",
			icon: Circle,
			renderContent: searchTerm => <div>Células content goes here. Termo: {searchTerm}</div>,
			onNew: searchTerm => {
				console.log("Novo em Células", searchTerm);
			},
			disabled: false,
		},
		{
			value: "componentes",
			label: "Componentes",
			icon: Component,
			renderContent: searchTerm => <div>Componentes content goes here. Termo: {searchTerm}</div>,
			onNew: searchTerm => {
				console.log("Novo em Componentes", searchTerm);
			},
			disabled: false,
		},
		{
			value: "vinculos",
			label: "Vínculos",
			icon: Link,
			renderContent: searchTerm => <div>Vínculos content goes here. Termo: {searchTerm}</div>,
			onNew: searchTerm => {
				console.log("Novo em Vínculos", searchTerm);
			},
			disabled: false,
		},
	];

	const activeTabItem = tabItems.find(tab => tab.value === activeTab);

	return (
		<div className="flex w-full flex-1 flex-col gap-4 *:relative">
			<Tabs value={activeTab} onValueChange={handleTabChange} className="relative flex w-full flex-col gap-2">
				<div className="top-0 z-10 h-auto pb-2">
					<RegisterInspectionHeader
						tabItems={tabItems}
						activeTab={activeTab}
						setActiveTab={setActiveTab}
						searchTerm={searchTerm}
						setSearchTerm={setSearchTerm}
						onNew={searchTerm => activeTabItem?.onNew(searchTerm)}
					/>
				</div>
				<div className="mt-4">
					{tabItems.map(({ value, renderContent }) => (
						<TabsContent key={value} value={value} className="mt-0">
							{renderContent(searchTerm)}
						</TabsContent>
					))}
				</div>
			</Tabs>
			<ModalCreateForm isOpen={formModal.isOpen} onClose={() => formModal.closeModal()} />
			<ModalCreateMeasures isOpen={measuresModal.isOpen} onClose={() => measuresModal.closeModal()} />
		</div>
	);
};
