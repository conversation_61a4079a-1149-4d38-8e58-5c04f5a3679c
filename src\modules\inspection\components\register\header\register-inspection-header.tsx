"use client";
import { TInspectionTabValue } from "@/modules/inspection/hooks/tabs/inspection-tabs.hook";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { TabsList, TabsTrigger } from "@/shared/components/shadcn/tabs";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, SheetTitle, SheetTrigger } from "@/shared/components/shadcn/sheet";
import { useIsMobile, useIsTablet } from "@/shared/hooks/utils/media-query.hook";
import { LucideIcon, Menu, Plus, Search } from "lucide-react";
import React, { useState } from "react";

interface IRegisterInspectionHeaderProps {
	tabItems: ITabItemRegisterInspectionTabs[];
	activeTab: TInspectionTabValue;
	setActiveTab: (tab: TInspectionTabValue) => void;
	searchTerm: string;
	setSearchTerm: (term: string) => void;
	onNew: (searchTerm: string) => void;
}

export interface ITabItemRegisterInspectionTabs {
	value: TInspectionTabValue;
	label: string;
	icon: LucideIcon;
	renderContent: (searchTerm: string) => React.ReactNode;
	onNew: (searchTerm: string) => void;
	disabled?: boolean;
}

export const RegisterInspectionHeader: React.FC<IRegisterInspectionHeaderProps> = ({ tabItems, activeTab, setActiveTab, searchTerm, setSearchTerm, onNew }) => {
	const isMobile = useIsMobile();
	const isTablet = useIsTablet();
	const [isSheetOpen, setIsSheetOpen] = useState(false);

	const activeTabItem = tabItems.find(tab => tab.value === activeTab);

	// Use mobile layout for both mobile and tablet to prevent overlap issues
	const shouldUseMobileLayout = isMobile || isTablet;

	const handleTabSelect = (value: TInspectionTabValue) => {
		setActiveTab(value);
		setIsSheetOpen(false);
	};

	const TabButton: React.FC<{ item: ITabItemRegisterInspectionTabs; variant?: "default" | "mobile" }> = ({ item, variant = "default" }) => {
		const { value, label, icon: Icon, disabled } = item;
		const isActive = activeTab === value;

		const baseClasses = `
			flex items-center gap-3 px-4 py-3 transition-all duration-200 ease-in-out
			${disabled ? "cursor-not-allowed border border-dashed border-gray-300 bg-gray-100 text-gray-400 opacity-60" : "cursor-pointer"}
		`;

		if (variant === "mobile") {
			return (
				<button
					onClick={() => !disabled && handleTabSelect(value)}
					disabled={disabled}
					className={` ${baseClasses} w-full justify-start rounded-xl text-left ${isActive ? "bg-primary scale-[1.03] text-white shadow-lg" : disabled ? "" : "hover:bg-primary/10 hover:text-primary bg-white"} border-none ${isActive ? "ring-primary/40 ring-2" : ""} `}
					title={disabled ? "Funcionalidade em breve" : label}
					style={{
						boxShadow: isActive ? "0 2px 8px rgba(0, 80, 180, 0.10)" : undefined,
						transition: "box-shadow 0.2s, transform 0.2s",
					}}
				>
					<div className={`flex items-center gap-3 ${isActive ? "" : "text-primary/80"}`}>
						<Icon className={`size-5 ${isActive ? "text-white" : "text-primary"}`} />
						<span className={`text-base font-semibold ${isActive ? "text-white" : "text-primary"}`}>{label}</span>
					</div>
					{disabled && <span className="ml-auto text-xs italic opacity-70">(Em breve)</span>}
				</button>
			);
		}

		return (
			<TabsTrigger
				key={value}
				value={value}
				disabled={disabled}
				className={`bg-secondary text-foreground data-[state=active]:bg-primary data-[state=active]:border-primary/20 flex items-center gap-2 rounded-lg border border-transparent px-3 py-2.5 text-sm font-medium transition-all duration-200 data-[state=active]:text-white data-[state=active]:shadow-md md:px-4 ${
					disabled
						? "cursor-not-allowed border-dashed border-gray-300 bg-gray-100 text-gray-400 opacity-60 hover:bg-gray-100"
						: "hover:bg-primary/10 hover:text-primary hover:border-primary/20 hover:shadow-sm"
				} `}
				tabIndex={disabled ? -1 : 0}
				aria-disabled={disabled}
				title={disabled ? "Funcionalidade em breve" : label}
				onClick={() => !disabled && setActiveTab(value)}
				data-state={activeTab === value ? "active" : undefined}
			>
				<Icon className="size-4 flex-shrink-0" />
				<span className="whitespace-nowrap">{label}</span>
				{disabled && <span className="ml-1 hidden text-xs opacity-70 sm:inline">(Em breve)</span>}
			</TabsTrigger>
		);
	};

	if (shouldUseMobileLayout) {
		return (
			<div className="mb-6 space-y-4">
				<div className="flex items-center justify-between gap-3">
					<Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
						<SheetTrigger asChild>
							<Button
								variant="outline"
								className="hover:border-primary/30 hover:bg-primary/5 flex-1 justify-between border-2 border-gray-200 bg-white transition-all duration-200"
							>
								<div className="flex items-center gap-2">
									{activeTabItem && <activeTabItem.icon className="size-4" />}
									<span className="font-medium">{activeTabItem?.label || "Selecionar"}</span>
								</div>
								<Menu className="text-muted-foreground size-4" />
							</Button>
						</SheetTrigger>
						<SheetContent side="bottom" className="h-[80vh] rounded-t-xl px-0">
							<SheetHeader className="px-6 pb-2">
								<SheetTitle className="text-left text-lg font-bold">Selecionar Seção</SheetTitle>
							</SheetHeader>
							<div className="grid gap-1 px-4 pb-4">
								{tabItems.map(item => (
									<TabButton key={item.value} item={item} variant="mobile" />
								))}
							</div>
						</SheetContent>
					</Sheet>

					<Button
						onClick={() => onNew(searchTerm)}
						className="bg-primary hover:bg-primary/90 px-4 text-white shadow-md transition-all duration-200 hover:shadow-lg"
					>
						<Plus className="size-4" />
					</Button>
				</div>

				<div className="relative">
					<Search className="text-muted-foreground absolute top-1/2 left-3 size-4 -translate-y-1/2" />
					<Input
						placeholder="Pesquisar..."
						value={searchTerm}
						onChange={e => setSearchTerm(e.target.value)}
						className="focus:border-primary/50 h-11 border-2 border-gray-200 bg-white pl-10 transition-all duration-200"
					/>
				</div>
			</div>
		);
	}

	return (
		<div className="mb-6 flex h-full flex-col gap-4">
			<div className="flex h-full flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
				<div className="w-full md:flex-1 lg:max-w-none">
					<TabsList
						className="scrollbar-thin scrollbar-thumb-primary/30 scrollbar-track-transparent flex w-full flex-nowrap gap-2 overflow-x-auto rounded-none bg-transparent p-0 md:justify-start"
						style={{ WebkitOverflowScrolling: "touch" }}
					>
						{tabItems.map(item => (
							<TabButton key={item.value} item={item} />
						))}
					</TabsList>
				</div>

				<div className="flex w-full flex-col items-stretch gap-3 sm:flex-row lg:w-auto lg:flex-shrink-0 lg:items-center">
					<div className="relative w-full sm:min-w-[280px] lg:w-80">
						<Search className="text-muted-foreground absolute top-1/2 left-3 size-4 -translate-y-1/2" />
						<Input
							placeholder="Pesquisar..."
							value={searchTerm}
							onChange={e => setSearchTerm(e.target.value)}
							className="focus:border-primary/50 h-10 border-2 border-gray-200 bg-white pl-10 transition-all duration-200"
						/>
					</div>
					<Button
						onClick={() => onNew(searchTerm)}
						className="bg-primary hover:bg-primary/90 h-10 px-6 font-medium text-white shadow-md transition-all duration-200 hover:shadow-lg sm:flex-shrink-0"
					>
						<Plus className="mr-2 size-4" />
						Novo
					</Button>
				</div>
			</div>
		</div>
	);
};
