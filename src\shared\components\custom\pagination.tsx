import { Chevron<PERSON>eft, ChevronRight, Chevrons<PERSON>eft, ChevronsRight } from "lucide-react";
import React from "react";

import { Button } from "../shadcn/button";
import { Label } from "../shadcn/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../shadcn/select";

interface PaginationProps {
	currentPage: number;
	totalPages: number;
	pageSize: number;
	totalItems: number;
	selectedCount?: number;
	pageSizeOptions?: number[];
	onPageChange: (page: number) => void;
	onPageSizeChange: (pageSize: number) => void;
	showPageSizeSelector?: boolean;
	showSelectedInfo?: boolean;
}

export const Pagination: React.FC<PaginationProps> = ({
	currentPage,
	totalPages,
	pageSize,
	totalItems,
	selectedCount = 0,
	pageSizeOptions = [10, 20, 30, 40, 50],
	onPageChange,
	onPageSizeChange,
	showPageSizeSelector = true,
	showSelectedInfo = false,
}) => {
	const canPreviousPage = currentPage > 1;
	const canNextPage = currentPage < totalPages;

	const handlePageChange = (page: number) => {
		if (page >= 1 && page <= totalPages) {
			onPageChange(page);
		}
	};

	const handlePageSizeChange = (value: string) => {
		onPageSizeChange(Number(value));
	};

	return (
		<div className="flex items-center justify-between px-4">
			{showSelectedInfo && (
				<div className="text-muted-foreground hidden flex-1 text-sm lg:flex">
					{selectedCount} de {totalItems} linhas selecionadas.
				</div>
			)}

			<div className="flex w-full items-center gap-8 lg:w-fit">
				{showPageSizeSelector && (
					<div className="hidden items-center gap-2 lg:flex">
						<Label htmlFor="rows-per-page" className="text-sm font-medium">
							Linhas por página
						</Label>
						<Select value={`${pageSize}`} onValueChange={handlePageSizeChange}>
							<SelectTrigger size="sm" className="w-20" id="rows-per-page">
								<SelectValue placeholder={pageSize} />
							</SelectTrigger>
							<SelectContent side="top">
								{pageSizeOptions.map(size => (
									<SelectItem key={size} value={`${size}`}>
										{size}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				)}

				<div className="flex w-fit items-center justify-center text-sm font-medium">
					Página {currentPage} de {totalPages}
				</div>

				<div className="ml-auto flex items-center gap-2 lg:ml-0">
					<Button variant="outline" className="hidden h-8 w-8 p-0 lg:flex" onClick={() => handlePageChange(1)} disabled={!canPreviousPage}>
						<span className="sr-only">Ir para a primeira página</span>
						<ChevronsLeft />
					</Button>

					<Button variant="outline" className="size-8" size="icon" onClick={() => handlePageChange(currentPage - 1)} disabled={!canPreviousPage}>
						<span className="sr-only">Ir para a página anterior</span>
						<ChevronLeft />
					</Button>

					<Button variant="outline" className="size-8" size="icon" onClick={() => handlePageChange(currentPage + 1)} disabled={!canNextPage}>
						<span className="sr-only">Ir para a próxima página</span>
						<ChevronRight />
					</Button>

					<Button
						variant="outline"
						className="hidden size-8 lg:flex"
						size="icon"
						onClick={() => handlePageChange(totalPages)}
						disabled={!canNextPage}
					>
						<span className="sr-only">Ir para a última página</span>
						<ChevronsRight />
					</Button>
				</div>
			</div>
		</div>
	);
};
