import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";
import { Control } from "react-hook-form";
import { FieldOptions } from "./field-options";

interface SortableRowProps {
	row: Row<IFieldTable>;
	onRowClick?: (tempId: string) => void;
	isSelected: boolean;
	control: Control<ICreateForm>;
	isFieldTypeOptions: (typeId: number) => boolean;
}

export const SortableRow: React.FC<SortableRowProps> = ({ row, onRowClick, isSelected, control }) => {
	const sortableId = `field-${row.index}`;
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id: sortableId,
		animateLayoutChanges: () => false,
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
	};

	const handleRowClick = () => {
		onRowClick?.(row.original.tempId || sortableId);
	};

	const fieldData = row.original;

	return (
		<>
			<TableRow
				ref={setNodeRef}
				style={style}
				className={`cursor-pointer ${isDragging ? "bg-muted/50" : ""} ${isSelected ? "!bg-muted" : ""}`}
				onClick={handleRowClick}
			>
				{row.getVisibleCells().map((cell, index) => (
					<TableCell key={cell.id}>
						{index === 0 ? (
							<div className="flex items-center gap-2">
								<div
									{...attributes}
									{...listeners}
									className="flex items-center justify-center cursor-grab active:cursor-grabbing p-1 rounded"
									onClick={e => e.stopPropagation()}
								>
									<GripVertical className="h-4 w-4 text-muted-foreground" />
								</div>
							</div>
						) : (
							flexRender(cell.column.columnDef.cell, cell.getContext())
						)}
					</TableCell>
				))}
			</TableRow>

			{isSelected && fieldData.typeId === InspectionFormTypeEnum.OPTIONS && (
				<TableRow className="border-0">
					<TableCell colSpan={row.getVisibleCells().length} className="p-0 border-0">
						<FieldOptions fieldIndex={row.index} control={control} field={fieldData} />
					</TableCell>
				</TableRow>
			)}
		</>
	);
};
