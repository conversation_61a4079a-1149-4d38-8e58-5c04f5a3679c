export interface IUpdateFormDTO {
	title: string;
	text?: string;
	nomenclature: string;
	developerId: number;
	approverId: number;
	fields: Field[];
}

interface Field {
	fieldId: number;
	nickname: string;
	required: boolean;
	group: number;
	sequence: number;
	typeId: number;
	measureId: number;
	groupTitle: string;
	biFilter: boolean;
	id: number;
	options: Option[];
}

interface Option {
	sequence: number;
	option: string;
	id: number;
}
