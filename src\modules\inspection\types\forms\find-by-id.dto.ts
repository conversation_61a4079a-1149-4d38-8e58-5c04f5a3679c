import { InspectionFormTypeEnum } from "../../constants/form/type-enum";

export interface IFormFindByIdDto {
	id: number;
	title: string;
	text: string;
	nomenclature: string;
	revision: string;
	developer: <PERSON><PERSON><PERSON>;
	approver: <PERSON><PERSON><PERSON>;
	fields: Field[];
}

interface Field {
	id: number;
	nickname: string;
	required: boolean;
	fieldType: <PERSON><PERSON><PERSON>;
	field: Dev<PERSON><PERSON>;
	measure: Measure;
	group: number;
	sequence: number;
	groupTitle: string;
	biFilter: boolean;
	options: Option[];
}

interface Option {
	id: number;
	sequence: number;
	option: string;
}

interface Measure {
	id: number;
	name: string;
	abbreviation: string;
}

interface Developer {
	id: InspectionFormTypeEnum;
	name: string;
}
