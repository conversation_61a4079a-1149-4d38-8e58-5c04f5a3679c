import { IUpdateFormDTO } from "../../types/forms/update-form.dto";
import { ICreateForm } from "../../validators/form/create";

export class InspectionFormToUpdateMapper {
	static map(data: ICreateForm): IUpdateFormDTO {
		return {
			title: data.title,
			text: data.text,
			nomenclature: data.nomenclature,
			developerId: Number(data.developerId),
			approverId: Number(data.approverId),
			fields: data.fields.map(field => ({
				fieldId: field.fieldId ?? 0,
				nickname: field.nickname ?? "",
				required: field.required ?? false,
				group: field.group ?? 1,
				sequence: field.sequence ?? 1,
				typeId: Number(field.typeId),
				measureId: Number(field.measureId),
				groupTitle: field.groupTitle ?? "",
				biFilter: field.biFilter ?? false,
				id: Number(field.id) || 0,
				options: (field.options ?? []).map((opt, optIdx) => ({
					sequence: opt.sequence ?? optIdx + 1,
					option: opt.option ?? "",
					id: opt.id ?? 0,
				})),
			})),
		};
	}
}
