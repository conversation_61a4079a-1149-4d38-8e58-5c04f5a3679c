"use client";
import { useCreateFormDragTable } from "@/modules/inspection/hooks/form/create/drag-table.hook";
// import { restrictToTableAreaVertical } from "@/modules/inspection/hooks/form/create/modifiers";
import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { Button } from "@/shared/components/shadcn/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { restrictToParentElement } from "@dnd-kit/modifiers";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { Control } from "react-hook-form";
import { createColumnsFieldForm } from "./columns";
import { SortableRow } from "./sortable-row";

interface TableCreateFormFieldsProps {
	fields: IFieldTable[];
	control: Control<ICreateForm>;
	addField: (field: Omit<IFieldTable, "sequence">) => void;
	removeField: (index: number) => void;
	updateField: (index: number, field: Partial<IFieldTable>) => void;
	reorderFields: (oldIndex: number, newIndex: number) => void;
	isFieldTypeOptions: (typeId: number) => boolean;
}

export const TableCreateFormFields: React.FC<TableCreateFormFieldsProps> = ({
	fields,
	addField,
	removeField,
	reorderFields,
	isFieldTypeOptions,
	control,
}) => {
	const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);
	const { sortableId, sensors, dataIds, handleDragStart, handleDragEnd, handleDragCancel } = useCreateFormDragTable({
		fields,
		reorderFields,
	});

	const columns = useMemo(
		() =>
			createColumnsFieldForm({
				control,
				onDelete: (index: number) => removeField(index),
			}),
		[control, removeField]
	);

	const table = useReactTable({
		data: fields,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getRowId: (_, index) => `field-${index}`,
	});

	const generateFieldId = () => `field-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

	return (
		<div className="flex flex-col gap-4 mt-2">
			<div className="flex justify-end w-full">
				<Button
					variant="outline"
					onClick={e => {
						e.preventDefault();
						addField({
							tempId: generateFieldId(),
							fieldId: undefined,
							groupTitle: "",
							nickname: "",
							required: false,
							group: 1,
							typeId: 1,
							measureId: 1,
							biFilter: false,
						});
					}}
				>
					<Plus className="ml-2 size-4" />
					Adicionar campo
				</Button>
			</div>
			<div className="overflow-auto max-h-[500px] w-full rounded-lg border" data-dnd-table-container>
				<DndContext
					collisionDetection={closestCenter}
					modifiers={[restrictToParentElement]}
					onDragStart={handleDragStart}
					onDragEnd={handleDragEnd}
					onDragCancel={handleDragCancel}
					sensors={sensors}
					id={sortableId}
				>
					<Table className="flex-1 w-full" data-dnd-table>
						<TableHeader className="bg-muted sticky top-0 z-10">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => (
										<TableHead key={header.id} colSpan={header.colSpan}>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									))}
								</TableRow>
							))}
						</TableHeader>

						<TableBody>
							{table.getRowModel().rows?.length ? (
								<SortableContext items={dataIds} strategy={verticalListSortingStrategy}>
									{table.getRowModel().rows.map(row => (
										<SortableRow
											key={row.id}
											row={row}
											onRowClick={setSelectedFieldId}
											isSelected={selectedFieldId === row.original.tempId || selectedFieldId === `field-${row.index}`}
											control={control}
											isFieldTypeOptions={isFieldTypeOptions}
										/>
									))}
								</SortableContext>
							) : (
								<TableRow>
									<TableCell colSpan={table.getAllLeafColumns().length} className="h-32 text-center">
										<div className="flex flex-col items-center gap-2 text-muted-foreground">
											<Plus className="h-8 w-8 opacity-50" />
											<div>
												<p className="font-medium">Nenhum campo adicionado</p>
												<p className="text-sm">{`Clique em "Adicionar campo" para começar`}</p>
											</div>
										</div>
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</DndContext>
			</div>
		</div>
	);
};
